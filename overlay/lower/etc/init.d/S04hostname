#!/bin/sh

. /usr/share/common

default_hostname() {
	echo "$(sed -n 's/^HOSTNAME=//p' $OS_RELEASE_FILE)"
}

is_user_modified_hostname() {
	local hostname default_hostname os_hostname

	hostname="$(hostname)"

	if [ -z "$hostname" ]; then
		echo_warning "Hostname is blank and should be generated"
		return 1
	fi

	if [ "(none)" = "$hostname" ]; then
		echo_warning "Hostname is '(none)' and should be generated"
		return 1
	fi

	if [ "$(default_hostname)" = "$hostname" ]; then
		echo_warning "Hostname $hostname is default and should be generated"
		return 1
	fi

	if echo "$hostname" | grep -i -q "^$(default_hostname)-[0-9a-f]\{4\}$"; then
		echo_warning "Hostname $hostname is auto-generated and should be regenerated if the MAC address has changed"
		return 1
	fi

	echo_info "Hostname $hostname is custom, skipping generation"

	return 0
}

set_hostname() {
	local new_hostname="$1"
	local source="$2"

	echo_info "Setting hostname to $new_hostname ($source)"

	if [ "$(hostname)" != "$new_hostname" ]; then
		echo_info "Updating current hostname"
		hostname "$new_hostname"
	fi

	# Do not change hostname in $OS_RELEASE_FILE!

	ip="*********"
	hostname_in_hosts=$(sed -nE "s/^$ip\s+(.*)$/\1/p" $HOSTS_FILE)
	echo_info "Hostname in $HOSTS_FILE is $hostname_in_hosts"

	if [ "$hostname_in_hosts" = "$new_hostname" ]; then
		echo_info "Hostname in $HOSTS_FILE is correct"
	else
		echo_info "Updating hostname in $HOSTS_FILE"
		sed -i "/^$ip/c$ip\t$new_hostname" $HOSTS_FILE
	fi

	hostname_in_hostname=$(cat $HOSTNAME_FILE)
	if [ "$hostname_in_hostname" = "$new_hostname" ]; then
		echo_info "Hostname in $HOSTNAME_FILE is correct"
	else
		echo_info "Saving hostname in $HOSTNAME_FILE"
		echo $new_hostname > $HOSTNAME_FILE
	fi
}

generate() {
	local generated_hostname="$(default_hostname)-$(echo $wlan_mac | sed -E 's/://g;s/.*(.{4})$/\1/')"
	echo_info "Generated name: $generated_hostname"
	set_hostname "$generated_hostname" "auto-generated"
}

start() {
	echo_title "Configuring hostname"

	# Check if hostname is configured via 'conf s hostname'
	if [ -n "$hostname" ]; then
		echo_info "Using configured hostname: $hostname"
		set_hostname "$hostname" "configured"
	elif is_user_modified_hostname; then
		echo_info "Hostname is already customized, keeping current"
	else
		generate
	fi

	current_hostname=$(hostname)
	echo_info "Hostname set to $current_hostname"

	HOSTNAME="$current_hostname"
	export HOSTNAME
}

case "$1" in
	start)
		start
		;;
	stop)
		true
		;;
	restart)
		start
		;;
	reset)
		generate
		;;
	*)
		echo "Usage: $0 {start|stop|restart}"
		exit 1
		;;
esac

exit 0
