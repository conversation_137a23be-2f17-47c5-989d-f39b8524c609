#!/bin/env sh

. /usr/share/common

show_help() {
	echo "Usage: $0 [options]
Where:
	-m message  Message to send to webhook.
	-s          Attach snapshot to the webhook.
	-c          Attach videoclip to the webhook.
	-u url      Webhook URL.
	-v          Verbose output.
	-h          Show this help.
" >&2
	exit 0
}

while getopts m:scu:vh flag; do
	case "$flag" in
		m) webhook_message=$OPTARG ;;
		s) webhook_attach_snapshot="true" ;;
		c) webhook_attach_videoclip="true" ;;
		u) webhook_url=$OPTARG ;;
		v) verbose="true" ;;
		h | *) show_help ;;
	esac
done

if [ -z "$webhook_url" ]; then
	echo_error "Webhook URL not found"
	exit 1
fi

if [ "true" = "$verbose" ]; then
	command="$CURL --verbose"
else
	command="$CURL --silent"
fi

# Handle snapshot attachment
if [ "true" = "$webhook_attach_snapshot" ]; then
	snapshot_attachment=$(mktemp -u).jpg
	cp -f "$SNAPSHOT_FILE" "$snapshot_attachment"
	command="$command -F \"image=@$snapshot_attachment\""
fi

# Handle videoclip attachment
if [ "true" = "$webhook_attach_videoclip" ]; then
	if [ -f "$VBUFFER_FILE" ]; then
		videoclip_attachment=$(mktemp -u).mp4
		cp -f "$VBUFFER_FILE" "$videoclip_attachment"
		command="$command -F \"videoclip=@$videoclip_attachment\""
	else
		echo_error "Videoclip not found: $VBUFFER_FILE"
		exit 1
	fi
fi

# Add message and url to the command
command="$command -F \"message=$webhook_message\" --url \"$webhook_url\""

if [ "true" = "$verbose" ]; then
	echo_command "$command"
fi

result=$(sh -c "$command" 2>&1)
if [ $? -eq 0 ] ; then
	if [ "true" = "$verbose" ]; then
		echo_info "$result"
	fi
else
	echo_error "Failed to send webhook message"
	exit 1
fi

# Cleanup temporary files
[ -f "$snapshot_attachment" ] && rm "$snapshot_attachment"
[ -f "$videoclip_attachment" ] && rm "$videoclip_attachment"

exit 0
