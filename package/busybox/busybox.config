#
# Automatically generated make config: don't edit
# Busybox version: 1.37.0
#
CONFIG_HAVE_DOT_CONFIG=y

#
# Settings
#
# CONFIG_DESKTOP is not set
# CONFIG_EXTRA_COMPAT is not set
# CONFIG_FEDORA_COMPAT is not set
# CONFIG_INCLUDE_SUSv2 is not set
CONFIG_LONG_OPTS=y
CONFIG_SHOW_USAGE=y
CONFIG_FEATURE_VERBOSE_USAGE=y
# CONFIG_FEATURE_COMPRESS_USAGE is not set
CONFIG_LFS=y
CONFIG_TIME64=y
# CONFIG_PAM is not set
# CONFIG_FEATURE_DEVPTS is not set
# CONFIG_FEATURE_UTMP is not set
# CONFIG_FEATURE_WTMP is not set
# CONFIG_FEATURE_PIDFILE is not set
CONFIG_PID_FILE_PATH=""
CONFIG_BUSYBOX=y
CONFIG_FEATURE_SHOW_SCRIPT=y
CONFIG_FEATURE_INSTALLER=y
# CONFIG_INSTALL_NO_USR is not set
CONFIG_FEATURE_SUID=y
# CONFIG_FEATURE_SUID_CONFIG is not set
# CONFIG_FEATURE_SUID_CONFIG_QUIET is not set
# CONFIG_FEATURE_PREFER_APPLETS is not set
CONFIG_BUSYBOX_EXEC_PATH="/proc/self/exe"
# CONFIG_SELINUX is not set
# CONFIG_FEATURE_CLEAN_UP is not set
CONFIG_FEATURE_SYSLOG_INFO=y
CONFIG_FEATURE_SYSLOG=y

#
# Build Options
#
# CONFIG_STATIC is not set
# CONFIG_PIE is not set
# CONFIG_NOMMU is not set
# CONFIG_BUILD_LIBBUSYBOX is not set
# CONFIG_FEATURE_LIBBUSYBOX_STATIC is not set
# CONFIG_FEATURE_INDIVIDUAL is not set
# CONFIG_FEATURE_SHARED_BUSYBOX is not set
CONFIG_CROSS_COMPILER_PREFIX=""
CONFIG_SYSROOT=""
CONFIG_EXTRA_CFLAGS=""
CONFIG_EXTRA_LDFLAGS=""
CONFIG_EXTRA_LDLIBS=""
# CONFIG_USE_PORTABLE_CODE is not set
# CONFIG_STACK_OPTIMIZATION_386 is not set
CONFIG_STATIC_LIBGCC=y

#
# Installation Options ("make install" behavior)
#
CONFIG_INSTALL_APPLET_SYMLINKS=y
# CONFIG_INSTALL_APPLET_HARDLINKS is not set
# CONFIG_INSTALL_APPLET_SCRIPT_WRAPPERS is not set
# CONFIG_INSTALL_APPLET_DONT is not set
# CONFIG_INSTALL_SH_APPLET_SYMLINK is not set
# CONFIG_INSTALL_SH_APPLET_HARDLINK is not set
# CONFIG_INSTALL_SH_APPLET_SCRIPT_WRAPPER is not set
CONFIG_PREFIX="./_install"

#
# Debugging Options
#
# CONFIG_DEBUG is not set
# CONFIG_DEBUG_PESSIMIZE is not set
# CONFIG_DEBUG_SANITIZE is not set
# CONFIG_UNIT_TEST is not set
# CONFIG_WERROR is not set
# CONFIG_WARN_SIMPLE_MSG is not set
CONFIG_NO_DEBUG_LIB=y
# CONFIG_DMALLOC is not set
# CONFIG_EFENCE is not set

#
# Library Tuning
#
# CONFIG_FEATURE_USE_BSS_TAIL is not set
CONFIG_FLOAT_DURATION=y
CONFIG_FEATURE_RTMINMAX=y
CONFIG_FEATURE_RTMINMAX_USE_LIBC_DEFINITIONS=y
CONFIG_FEATURE_BUFFERS_USE_MALLOC=y
# CONFIG_FEATURE_BUFFERS_GO_ON_STACK is not set
# CONFIG_FEATURE_BUFFERS_GO_IN_BSS is not set
CONFIG_PASSWORD_MINLEN=6
CONFIG_MD5_SMALL=1
CONFIG_SHA1_SMALL=3
CONFIG_SHA1_HWACCEL=y
CONFIG_SHA256_HWACCEL=y
CONFIG_SHA3_SMALL=1
CONFIG_FEATURE_NON_POSIX_CP=y
# CONFIG_FEATURE_VERBOSE_CP_MESSAGE is not set
CONFIG_FEATURE_USE_SENDFILE=y
CONFIG_FEATURE_COPYBUF_KB=4
CONFIG_MONOTONIC_SYSCALL=y
CONFIG_IOCTL_HEX2STR_ERROR=y
CONFIG_FEATURE_EDITING=y
CONFIG_FEATURE_EDITING_MAX_LEN=1024
CONFIG_FEATURE_EDITING_VI=y
CONFIG_FEATURE_EDITING_HISTORY=999
CONFIG_FEATURE_EDITING_SAVEHISTORY=y
# CONFIG_FEATURE_EDITING_SAVE_ON_EXIT is not set
CONFIG_FEATURE_REVERSE_SEARCH=y
CONFIG_FEATURE_TAB_COMPLETION=y
# CONFIG_FEATURE_USERNAME_COMPLETION is not set
CONFIG_FEATURE_EDITING_FANCY_PROMPT=y
CONFIG_FEATURE_EDITING_WINCH=y
# CONFIG_FEATURE_EDITING_ASK_TERMINAL is not set
# CONFIG_LOCALE_SUPPORT is not set
CONFIG_UNICODE_SUPPORT=y
# CONFIG_UNICODE_USING_LOCALE is not set
# CONFIG_FEATURE_CHECK_UNICODE_IN_ENV is not set
CONFIG_SUBST_WCHAR=0
CONFIG_LAST_SUPPORTED_WCHAR=0
# CONFIG_UNICODE_COMBINING_WCHARS is not set
# CONFIG_UNICODE_WIDE_WCHARS is not set
# CONFIG_UNICODE_BIDI_SUPPORT is not set
# CONFIG_UNICODE_NEUTRAL_TABLE is not set
# CONFIG_UNICODE_PRESERVE_BROKEN is not set
# CONFIG_LOOP_CONFIGURE is not set
# CONFIG_NO_LOOP_CONFIGURE is not set
CONFIG_TRY_LOOP_CONFIGURE=y

#
# Applets
#

#
# Archival Utilities
#
# CONFIG_FEATURE_SEAMLESS_XZ is not set
# CONFIG_FEATURE_SEAMLESS_LZMA is not set
# CONFIG_FEATURE_SEAMLESS_BZ2 is not set
# CONFIG_FEATURE_SEAMLESS_GZ is not set
# CONFIG_FEATURE_SEAMLESS_Z is not set
# CONFIG_AR is not set
# CONFIG_FEATURE_AR_LONG_FILENAMES is not set
# CONFIG_FEATURE_AR_CREATE is not set
# CONFIG_UNCOMPRESS is not set
CONFIG_GUNZIP=y
CONFIG_ZCAT=y
CONFIG_FEATURE_GUNZIP_LONG_OPTIONS=y
CONFIG_BUNZIP2=y
CONFIG_BZCAT=y
CONFIG_UNLZMA=y
CONFIG_LZCAT=y
CONFIG_LZMA=y
CONFIG_UNXZ=y
CONFIG_XZCAT=y
CONFIG_XZ=y
# CONFIG_BZIP2 is not set
CONFIG_BZIP2_SMALL=0
CONFIG_FEATURE_BZIP2_DECOMPRESS=y
# CONFIG_CPIO is not set
# CONFIG_FEATURE_CPIO_O is not set
# CONFIG_FEATURE_CPIO_P is not set
# CONFIG_FEATURE_CPIO_IGNORE_DEVNO is not set
# CONFIG_FEATURE_CPIO_RENUMBER_INODES is not set
# CONFIG_DPKG is not set
# CONFIG_DPKG_DEB is not set
CONFIG_GZIP=y
# CONFIG_FEATURE_GZIP_LONG_OPTIONS is not set
CONFIG_GZIP_FAST=0
# CONFIG_FEATURE_GZIP_LEVELS is not set
CONFIG_FEATURE_GZIP_DECOMPRESS=y
# CONFIG_LZOP is not set
# CONFIG_UNLZOP is not set
# CONFIG_LZOPCAT is not set
# CONFIG_LZOP_COMPR_HIGH is not set
# CONFIG_RPM is not set
# CONFIG_RPM2CPIO is not set
CONFIG_TAR=y
CONFIG_FEATURE_TAR_LONG_OPTIONS=y
CONFIG_FEATURE_TAR_CREATE=y
# CONFIG_FEATURE_TAR_AUTODETECT is not set
CONFIG_FEATURE_TAR_FROM=y
# CONFIG_FEATURE_TAR_OLDGNU_COMPATIBILITY is not set
# CONFIG_FEATURE_TAR_OLDSUN_COMPATIBILITY is not set
CONFIG_FEATURE_TAR_GNU_EXTENSIONS=y
CONFIG_FEATURE_TAR_TO_COMMAND=y
# CONFIG_FEATURE_TAR_UNAME_GNAME is not set
# CONFIG_FEATURE_TAR_NOPRESERVE_TIME is not set
# CONFIG_FEATURE_TAR_SELINUX is not set
CONFIG_UNZIP=y
CONFIG_FEATURE_UNZIP_CDF=y
# CONFIG_FEATURE_UNZIP_BZIP2 is not set
# CONFIG_FEATURE_UNZIP_LZMA is not set
# CONFIG_FEATURE_UNZIP_XZ is not set
# CONFIG_FEATURE_LZMA_FAST is not set

#
# Coreutils
#
CONFIG_FEATURE_VERBOSE=y

#
# Common options for date and touch
#
# CONFIG_FEATURE_TIMEZONE is not set

#
# Common options for cp and mv
#
CONFIG_FEATURE_PRESERVE_HARDLINKS=y

#
# Common options for df, du, ls
#
CONFIG_FEATURE_HUMAN_READABLE=y
CONFIG_BASENAME=y
CONFIG_CAT=y
CONFIG_FEATURE_CATN=y
CONFIG_FEATURE_CATV=y
CONFIG_CHGRP=y
CONFIG_CHMOD=y
CONFIG_CHOWN=y
# CONFIG_FEATURE_CHOWN_LONG_OPTIONS is not set
CONFIG_CHROOT=y
# CONFIG_CKSUM is not set
CONFIG_CRC32=y
# CONFIG_COMM is not set
CONFIG_CP=y
# CONFIG_FEATURE_CP_LONG_OPTIONS is not set
# CONFIG_FEATURE_CP_REFLINK is not set
CONFIG_CUT=y
CONFIG_FEATURE_CUT_REGEX=y
CONFIG_DATE=y
CONFIG_FEATURE_DATE_ISOFMT=y
# CONFIG_FEATURE_DATE_NANO is not set
CONFIG_FEATURE_DATE_COMPAT=y
CONFIG_DD=y
CONFIG_FEATURE_DD_SIGNAL_HANDLING=y
# CONFIG_FEATURE_DD_THIRD_STATUS_LINE is not set
CONFIG_FEATURE_DD_IBS_OBS=y
CONFIG_FEATURE_DD_STATUS=y
CONFIG_DF=y
CONFIG_FEATURE_DF_FANCY=y
CONFIG_FEATURE_SKIP_ROOTFS=y
CONFIG_DIRNAME=y
# CONFIG_DOS2UNIX is not set
# CONFIG_UNIX2DOS is not set
CONFIG_DU=y
CONFIG_FEATURE_DU_DEFAULT_BLOCKSIZE_1K=y
CONFIG_ECHO=y
CONFIG_FEATURE_FANCY_ECHO=y
CONFIG_ENV=y
# CONFIG_EXPAND is not set
# CONFIG_UNEXPAND is not set
CONFIG_EXPR=y
CONFIG_EXPR_MATH_SUPPORT_64=y
# CONFIG_FACTOR is not set
CONFIG_FALSE=y
# CONFIG_FOLD is not set
CONFIG_HEAD=y
CONFIG_FEATURE_FANCY_HEAD=y
# CONFIG_HOSTID is not set
CONFIG_ID=y
# CONFIG_GROUPS is not set
# CONFIG_INSTALL is not set
# CONFIG_FEATURE_INSTALL_LONG_OPTIONS is not set
CONFIG_LINK=y
CONFIG_LN=y
# CONFIG_LOGNAME is not set
CONFIG_LS=y
CONFIG_FEATURE_LS_FILETYPES=y
CONFIG_FEATURE_LS_FOLLOWLINKS=y
CONFIG_FEATURE_LS_RECURSIVE=y
CONFIG_FEATURE_LS_WIDTH=y
CONFIG_FEATURE_LS_SORTFILES=y
CONFIG_FEATURE_LS_TIMESTAMPS=y
CONFIG_FEATURE_LS_USERNAME=y
CONFIG_FEATURE_LS_COLOR=y
CONFIG_FEATURE_LS_COLOR_IS_DEFAULT=y
CONFIG_MD5SUM=y
# CONFIG_SHA1SUM is not set
CONFIG_SHA256SUM=y
# CONFIG_SHA512SUM is not set
# CONFIG_SHA3SUM is not set

#
# Common options for md5sum, sha1sum, sha256sum, sha512sum, sha3sum
#
CONFIG_FEATURE_MD5_SHA1_SUM_CHECK=y
CONFIG_MKDIR=y
CONFIG_MKFIFO=y
CONFIG_MKNOD=y
CONFIG_MKTEMP=y
CONFIG_MV=y
CONFIG_NICE=y
CONFIG_NL=y
CONFIG_NOHUP=y
# CONFIG_NPROC is not set
CONFIG_OD=y
# CONFIG_PASTE is not set
CONFIG_PRINTENV=y
CONFIG_PRINTF=y
CONFIG_PWD=y
CONFIG_READLINK=y
CONFIG_FEATURE_READLINK_FOLLOW=y
CONFIG_REALPATH=y
CONFIG_RM=y
# CONFIG_RMDIR is not set
CONFIG_SEQ=y
CONFIG_SHRED=y
# CONFIG_SHUF is not set
CONFIG_SLEEP=y
CONFIG_FEATURE_FANCY_SLEEP=y
CONFIG_SORT=y
CONFIG_FEATURE_SORT_BIG=y
# CONFIG_FEATURE_SORT_OPTIMIZE_MEMORY is not set
# CONFIG_SPLIT is not set
# CONFIG_FEATURE_SPLIT_FANCY is not set
CONFIG_STAT=y
CONFIG_FEATURE_STAT_FORMAT=y
# CONFIG_FEATURE_STAT_FILESYSTEM is not set
CONFIG_STTY=y
# CONFIG_SUM is not set
CONFIG_SYNC=y
# CONFIG_FEATURE_SYNC_FANCY is not set
# CONFIG_FSYNC is not set
# CONFIG_TAC is not set
CONFIG_TAIL=y
CONFIG_FEATURE_FANCY_TAIL=y
CONFIG_TEE=y
CONFIG_FEATURE_TEE_USE_BLOCK_IO=y
CONFIG_TEST=y
CONFIG_TEST1=y
CONFIG_TEST2=y
CONFIG_FEATURE_TEST_64=y
CONFIG_TIMEOUT=y
CONFIG_TOUCH=y
# CONFIG_FEATURE_TOUCH_SUSV3 is not set
CONFIG_TR=y
CONFIG_FEATURE_TR_CLASSES=y
# CONFIG_FEATURE_TR_EQUIV is not set
CONFIG_TRUE=y
CONFIG_TRUNCATE=y
# CONFIG_TSORT is not set
CONFIG_TTY=y
CONFIG_UNAME=y
CONFIG_UNAME_OSNAME="GNU/Linux"
# CONFIG_BB_ARCH is not set
CONFIG_UNIQ=y
CONFIG_UNLINK=y
CONFIG_USLEEP=y
# CONFIG_UUDECODE is not set
# CONFIG_BASE32 is not set
CONFIG_BASE64=y
# CONFIG_UUENCODE is not set
CONFIG_WC=y
# CONFIG_FEATURE_WC_LARGE is not set
# CONFIG_WHO is not set
# CONFIG_W is not set
# CONFIG_USERS is not set
# CONFIG_WHOAMI is not set
CONFIG_YES=y

#
# Console Utilities
#
# CONFIG_CHVT is not set
CONFIG_CLEAR=y
# CONFIG_DEALLOCVT is not set
# CONFIG_DUMPKMAP is not set
# CONFIG_FGCONSOLE is not set
# CONFIG_KBD_MODE is not set
# CONFIG_LOADFONT is not set
# CONFIG_SETFONT is not set
# CONFIG_FEATURE_SETFONT_TEXTUAL_MAP is not set
CONFIG_DEFAULT_SETFONT_DIR=""
# CONFIG_FEATURE_LOADFONT_PSF2 is not set
# CONFIG_FEATURE_LOADFONT_RAW is not set
# CONFIG_LOADKMAP is not set
# CONFIG_OPENVT is not set
CONFIG_RESET=y
CONFIG_RESIZE=y
CONFIG_FEATURE_RESIZE_PRINT=y
CONFIG_SETCONSOLE=y
# CONFIG_FEATURE_SETCONSOLE_LONG_OPTIONS is not set
CONFIG_SETKEYCODES=y
# CONFIG_SETLOGCONS is not set
# CONFIG_SHOWKEY is not set

#
# Debian Utilities
#
CONFIG_PIPE_PROGRESS=y
CONFIG_RUN_PARTS=y
CONFIG_FEATURE_RUN_PARTS_LONG_OPTIONS=y
# CONFIG_FEATURE_RUN_PARTS_FANCY is not set
CONFIG_START_STOP_DAEMON=y
CONFIG_FEATURE_START_STOP_DAEMON_LONG_OPTIONS=y
CONFIG_FEATURE_START_STOP_DAEMON_FANCY=y
CONFIG_WHICH=y

#
# klibc-utils
#
# CONFIG_MINIPS is not set
# CONFIG_NUKE is not set
# CONFIG_RESUME is not set
CONFIG_RUN_INIT=y

#
# Editors
#
CONFIG_AWK=y
# CONFIG_FEATURE_AWK_LIBM is not set
CONFIG_FEATURE_AWK_GNU_EXTENSIONS=y
CONFIG_CMP=y
CONFIG_DIFF=y
# CONFIG_FEATURE_DIFF_LONG_OPTIONS is not set
CONFIG_FEATURE_DIFF_DIR=y
# CONFIG_ED is not set
CONFIG_PATCH=y
CONFIG_SED=y
CONFIG_VI=y
CONFIG_FEATURE_VI_MAX_LEN=4096
CONFIG_FEATURE_VI_8BIT=y
CONFIG_FEATURE_VI_COLON=y
CONFIG_FEATURE_VI_COLON_EXPAND=y
CONFIG_FEATURE_VI_YANKMARK=y
CONFIG_FEATURE_VI_SEARCH=y
# CONFIG_FEATURE_VI_REGEX_SEARCH is not set
CONFIG_FEATURE_VI_USE_SIGNALS=y
CONFIG_FEATURE_VI_DOT_CMD=y
CONFIG_FEATURE_VI_READONLY=y
CONFIG_FEATURE_VI_SETOPTS=y
CONFIG_FEATURE_VI_SET=y
CONFIG_FEATURE_VI_WIN_RESIZE=y
CONFIG_FEATURE_VI_ASK_TERMINAL=y
CONFIG_FEATURE_VI_UNDO=y
CONFIG_FEATURE_VI_UNDO_QUEUE=y
CONFIG_FEATURE_VI_UNDO_QUEUE_MAX=256
CONFIG_FEATURE_VI_VERBOSE_STATUS=y
CONFIG_FEATURE_ALLOW_EXEC=y

#
# Finding Utilities
#
CONFIG_FIND=y
CONFIG_FEATURE_FIND_PRINT0=y
CONFIG_FEATURE_FIND_MTIME=y
CONFIG_FEATURE_FIND_ATIME=y
CONFIG_FEATURE_FIND_CTIME=y
CONFIG_FEATURE_FIND_MMIN=y
CONFIG_FEATURE_FIND_AMIN=y
CONFIG_FEATURE_FIND_CMIN=y
CONFIG_FEATURE_FIND_PERM=y
CONFIG_FEATURE_FIND_TYPE=y
CONFIG_FEATURE_FIND_EXECUTABLE=y
CONFIG_FEATURE_FIND_XDEV=y
CONFIG_FEATURE_FIND_MAXDEPTH=y
CONFIG_FEATURE_FIND_NEWER=y
# CONFIG_FEATURE_FIND_INUM is not set
CONFIG_FEATURE_FIND_SAMEFILE=y
CONFIG_FEATURE_FIND_EXEC=y
CONFIG_FEATURE_FIND_EXEC_PLUS=y
CONFIG_FEATURE_FIND_EXEC_OK=y
CONFIG_FEATURE_FIND_USER=y
CONFIG_FEATURE_FIND_GROUP=y
CONFIG_FEATURE_FIND_NOT=y
CONFIG_FEATURE_FIND_DEPTH=y
CONFIG_FEATURE_FIND_PAREN=y
CONFIG_FEATURE_FIND_SIZE=y
CONFIG_FEATURE_FIND_PRUNE=y
CONFIG_FEATURE_FIND_QUIT=y
CONFIG_FEATURE_FIND_DELETE=y
CONFIG_FEATURE_FIND_EMPTY=y
CONFIG_FEATURE_FIND_PATH=y
CONFIG_FEATURE_FIND_REGEX=y
# CONFIG_FEATURE_FIND_CONTEXT is not set
# CONFIG_FEATURE_FIND_LINKS is not set
CONFIG_GREP=y
CONFIG_EGREP=y
CONFIG_FGREP=y
CONFIG_FEATURE_GREP_CONTEXT=y
CONFIG_XARGS=y
# CONFIG_FEATURE_XARGS_SUPPORT_CONFIRMATION is not set
CONFIG_FEATURE_XARGS_SUPPORT_QUOTES=y
CONFIG_FEATURE_XARGS_SUPPORT_TERMOPT=y
CONFIG_FEATURE_XARGS_SUPPORT_ZERO_TERM=y
CONFIG_FEATURE_XARGS_SUPPORT_REPL_STR=y
CONFIG_FEATURE_XARGS_SUPPORT_PARALLEL=y
CONFIG_FEATURE_XARGS_SUPPORT_ARGS_FILE=y

#
# Init Utilities
#
# CONFIG_BOOTCHARTD is not set
# CONFIG_FEATURE_BOOTCHARTD_BLOATED_HEADER is not set
# CONFIG_FEATURE_BOOTCHARTD_CONFIG_FILE is not set
# CONFIG_HALT is not set
# CONFIG_POWEROFF is not set
CONFIG_REBOOT=y
CONFIG_FEATURE_WAIT_FOR_INIT=y
# CONFIG_FEATURE_CALL_TELINIT is not set
CONFIG_TELINIT_PATH=""
CONFIG_INIT=y
CONFIG_LINUXRC=y
CONFIG_FEATURE_USE_INITTAB=y
CONFIG_FEATURE_KILL_REMOVED=y
CONFIG_FEATURE_KILL_DELAY=0
CONFIG_FEATURE_INIT_SCTTY=y
CONFIG_FEATURE_INIT_SYSLOG=y
CONFIG_FEATURE_INIT_QUIET=y
# CONFIG_FEATURE_INIT_COREDUMPS is not set
CONFIG_INIT_TERMINAL_TYPE="linux"
CONFIG_FEATURE_INIT_MODIFY_CMDLINE=y

#
# Login/Password Management Utilities
#
CONFIG_FEATURE_SHADOWPASSWDS=y
# CONFIG_USE_BB_PWD_GRP is not set
# CONFIG_USE_BB_SHADOW is not set
CONFIG_USE_BB_CRYPT=y
CONFIG_USE_BB_CRYPT_SHA=y
# CONFIG_ADD_SHELL is not set
# CONFIG_REMOVE_SHELL is not set
CONFIG_ADDGROUP=y
# CONFIG_FEATURE_ADDUSER_TO_GROUP is not set
CONFIG_ADDUSER=y
# CONFIG_FEATURE_CHECK_NAMES is not set
CONFIG_LAST_ID=60000
CONFIG_FIRST_SYSTEM_ID=100
CONFIG_LAST_SYSTEM_ID=999
CONFIG_CHPASSWD=y
CONFIG_FEATURE_DEFAULT_PASSWD_ALGO="sha512"
# CONFIG_CRYPTPW is not set
CONFIG_MKPASSWD=y
CONFIG_DELUSER=y
CONFIG_DELGROUP=y
# CONFIG_FEATURE_DEL_USER_FROM_GROUP is not set
CONFIG_GETTY=y
CONFIG_LOGIN=y
# CONFIG_LOGIN_SESSION_AS_CHILD is not set
# CONFIG_LOGIN_SCRIPTS is not set
CONFIG_FEATURE_NOLOGIN=y
CONFIG_FEATURE_SECURETTY=y
CONFIG_PASSWD=y
# CONFIG_FEATURE_PASSWD_WEAK_CHECK is not set
# CONFIG_SU is not set
# CONFIG_FEATURE_SU_SYSLOG is not set
# CONFIG_FEATURE_SU_CHECKS_SHELLS is not set
# CONFIG_FEATURE_SU_BLANK_PW_NEEDS_SECURE_TTY is not set
CONFIG_SULOGIN=y
# CONFIG_VLOCK is not set

#
# Linux Ext2 FS Progs
#
CONFIG_CHATTR=y
CONFIG_FSCK=y
CONFIG_LSATTR=y
# CONFIG_TUNE2FS is not set

#
# Linux Module Utilities
#
# CONFIG_MODPROBE_SMALL is not set
CONFIG_DEPMOD=y
CONFIG_INSMOD=y
CONFIG_LSMOD=y
CONFIG_FEATURE_LSMOD_PRETTY_2_6_OUTPUT=y
CONFIG_MODINFO=y
CONFIG_MODPROBE=y
# CONFIG_FEATURE_MODPROBE_BLACKLIST is not set
CONFIG_RMMOD=y

#
# Options common to multiple modutils
#
CONFIG_FEATURE_CMDLINE_MODULE_OPTIONS=y
# CONFIG_FEATURE_MODPROBE_SMALL_CHECK_ALREADY_LOADED is not set
# CONFIG_FEATURE_2_4_MODULES is not set
# CONFIG_FEATURE_INSMOD_VERSION_CHECKING is not set
# CONFIG_FEATURE_INSMOD_KSYMOOPS_SYMBOLS is not set
# CONFIG_FEATURE_INSMOD_LOADINKMEM is not set
# CONFIG_FEATURE_INSMOD_LOAD_MAP is not set
# CONFIG_FEATURE_INSMOD_LOAD_MAP_FULL is not set
CONFIG_FEATURE_CHECK_TAINTED_MODULE=y
# CONFIG_FEATURE_INSMOD_TRY_MMAP is not set
CONFIG_FEATURE_MODUTILS_ALIAS=y
CONFIG_FEATURE_MODUTILS_SYMBOLS=y
CONFIG_DEFAULT_MODULES_DIR="/lib/modules"
CONFIG_DEFAULT_DEPMOD_FILE="modules.dep"

#
# Linux System Utilities
#
# CONFIG_ACPID is not set
# CONFIG_FEATURE_ACPID_COMPAT is not set
# CONFIG_BLKDISCARD is not set
CONFIG_BLKID=y
# CONFIG_FEATURE_BLKID_TYPE is not set
# CONFIG_BLOCKDEV is not set
# CONFIG_CAL is not set
CONFIG_CHRT=y
CONFIG_DMESG=y
CONFIG_FEATURE_DMESG_PRETTY=y
# CONFIG_EJECT is not set
# CONFIG_FEATURE_EJECT_SCSI is not set
CONFIG_FALLOCATE=y
# CONFIG_FATATTR is not set
# CONFIG_FBSET is not set
# CONFIG_FEATURE_FBSET_FANCY is not set
# CONFIG_FEATURE_FBSET_READMODE is not set
# CONFIG_FDFORMAT is not set
CONFIG_FDISK=y
# CONFIG_FDISK_SUPPORT_LARGE_DISKS is not set
CONFIG_FEATURE_FDISK_WRITABLE=y
# CONFIG_FEATURE_AIX_LABEL is not set
# CONFIG_FEATURE_SGI_LABEL is not set
# CONFIG_FEATURE_SUN_LABEL is not set
# CONFIG_FEATURE_OSF_LABEL is not set
CONFIG_FEATURE_GPT_LABEL=y
CONFIG_FEATURE_FDISK_ADVANCED=y
# CONFIG_FINDFS is not set
CONFIG_FLOCK=y
# CONFIG_FDFLUSH is not set
CONFIG_FREERAMDISK=y
# CONFIG_FSCK_MINIX is not set
CONFIG_FSFREEZE=y
CONFIG_FSTRIM=y
CONFIG_GETOPT=y
CONFIG_FEATURE_GETOPT_LONG=y
CONFIG_HEXDUMP=y
# CONFIG_HD is not set
CONFIG_XXD=y
CONFIG_HWCLOCK=y
CONFIG_FEATURE_HWCLOCK_ADJTIME_FHS=y
# CONFIG_IONICE is not set
CONFIG_IPCRM=y
CONFIG_IPCS=y
# CONFIG_LAST is not set
# CONFIG_FEATURE_LAST_FANCY is not set
CONFIG_LOSETUP=y
# CONFIG_LSPCI is not set
CONFIG_LSUSB=y
CONFIG_MDEV=y
CONFIG_FEATURE_MDEV_CONF=y
CONFIG_FEATURE_MDEV_RENAME=y
# CONFIG_FEATURE_MDEV_RENAME_REGEXP is not set
CONFIG_FEATURE_MDEV_EXEC=y
CONFIG_FEATURE_MDEV_LOAD_FIRMWARE=y
CONFIG_FEATURE_MDEV_DAEMON=y
CONFIG_MESG=y
CONFIG_FEATURE_MESG_ENABLE_ONLY_GROUP=y
# CONFIG_MKE2FS is not set
CONFIG_MKFS_EXT2=y
# CONFIG_MKFS_MINIX is not set
# CONFIG_FEATURE_MINIX2 is not set
# CONFIG_MKFS_REISER is not set
# CONFIG_MKDOSFS is not set
CONFIG_MKFS_VFAT=y
# CONFIG_MKSWAP is not set
# CONFIG_FEATURE_MKSWAP_UUID is not set
# CONFIG_MORE is not set
CONFIG_MOUNT=y
# CONFIG_FEATURE_MOUNT_FAKE is not set
# CONFIG_FEATURE_MOUNT_VERBOSE is not set
# CONFIG_FEATURE_MOUNT_HELPERS is not set
# CONFIG_FEATURE_MOUNT_LABEL is not set
# CONFIG_FEATURE_MOUNT_NFS is not set
CONFIG_FEATURE_MOUNT_CIFS=y
CONFIG_FEATURE_MOUNT_FLAGS=y
CONFIG_FEATURE_MOUNT_FSTAB=y
CONFIG_FEATURE_MOUNT_OTHERTAB=y
CONFIG_MOUNTPOINT=y
CONFIG_NOLOGIN=y
CONFIG_NOLOGIN_DEPENDENCIES=y
# CONFIG_NSENTER is not set
CONFIG_PIVOT_ROOT=y
# CONFIG_RDATE is not set
# CONFIG_RDEV is not set
# CONFIG_READPROFILE is not set
CONFIG_RENICE=y
# CONFIG_REV is not set
# CONFIG_RTCWAKE is not set
# CONFIG_SCRIPT is not set
# CONFIG_SCRIPTREPLAY is not set
# CONFIG_SETARCH is not set
# CONFIG_LINUX32 is not set
# CONFIG_LINUX64 is not set
CONFIG_SETPRIV=y
CONFIG_FEATURE_SETPRIV_DUMP=y
CONFIG_FEATURE_SETPRIV_CAPABILITIES=y
CONFIG_FEATURE_SETPRIV_CAPABILITY_NAMES=y
CONFIG_SETSID=y
# CONFIG_SWAPON is not set
# CONFIG_FEATURE_SWAPON_DISCARD is not set
# CONFIG_FEATURE_SWAPON_PRI is not set
# CONFIG_SWAPOFF is not set
# CONFIG_FEATURE_SWAPONOFF_LABEL is not set
CONFIG_SWITCH_ROOT=y
# CONFIG_TASKSET is not set
# CONFIG_FEATURE_TASKSET_FANCY is not set
# CONFIG_FEATURE_TASKSET_CPULIST is not set
CONFIG_UEVENT=y
CONFIG_UMOUNT=y
CONFIG_FEATURE_UMOUNT_ALL=y
# CONFIG_UNSHARE is not set
# CONFIG_WALL is not set

#
# Common options for mount/umount
#
CONFIG_FEATURE_MOUNT_LOOP=y
CONFIG_FEATURE_MOUNT_LOOP_CREATE=y
# CONFIG_FEATURE_MTAB_SUPPORT is not set
CONFIG_VOLUMEID=y

#
# Filesystem/Volume identification
#
# CONFIG_FEATURE_VOLUMEID_BCACHE is not set
# CONFIG_FEATURE_VOLUMEID_BTRFS is not set
# CONFIG_FEATURE_VOLUMEID_CRAMFS is not set
# CONFIG_FEATURE_VOLUMEID_EROFS is not set
CONFIG_FEATURE_VOLUMEID_EXFAT=y
# CONFIG_FEATURE_VOLUMEID_EXT is not set
# CONFIG_FEATURE_VOLUMEID_F2FS is not set
CONFIG_FEATURE_VOLUMEID_FAT=y
# CONFIG_FEATURE_VOLUMEID_HFS is not set
# CONFIG_FEATURE_VOLUMEID_ISO9660 is not set
# CONFIG_FEATURE_VOLUMEID_JFS is not set
# CONFIG_FEATURE_VOLUMEID_LFS is not set
# CONFIG_FEATURE_VOLUMEID_LINUXRAID is not set
# CONFIG_FEATURE_VOLUMEID_LINUXSWAP is not set
# CONFIG_FEATURE_VOLUMEID_LUKS is not set
# CONFIG_FEATURE_VOLUMEID_MINIX is not set
# CONFIG_FEATURE_VOLUMEID_NILFS is not set
# CONFIG_FEATURE_VOLUMEID_NTFS is not set
# CONFIG_FEATURE_VOLUMEID_OCFS2 is not set
# CONFIG_FEATURE_VOLUMEID_REISERFS is not set
# CONFIG_FEATURE_VOLUMEID_ROMFS is not set
# CONFIG_FEATURE_VOLUMEID_SQUASHFS is not set
# CONFIG_FEATURE_VOLUMEID_SYSV is not set
# CONFIG_FEATURE_VOLUMEID_UBIFS is not set
# CONFIG_FEATURE_VOLUMEID_UDF is not set
# CONFIG_FEATURE_VOLUMEID_XFS is not set

#
# Miscellaneous Utilities
#
# CONFIG_ADJTIMEX is not set
# CONFIG_ASCII is not set
# CONFIG_BBCONFIG is not set
# CONFIG_FEATURE_COMPRESS_BBCONFIG is not set
CONFIG_BC=y
# CONFIG_DC is not set
CONFIG_FEATURE_DC_BIG=y
# CONFIG_FEATURE_DC_LIBM is not set
# CONFIG_FEATURE_BC_INTERACTIVE is not set
# CONFIG_FEATURE_BC_LONG_OPTIONS is not set
# CONFIG_BEEP is not set
CONFIG_FEATURE_BEEP_FREQ=0
CONFIG_FEATURE_BEEP_LENGTH_MS=0
# CONFIG_CHAT is not set
# CONFIG_FEATURE_CHAT_NOFAIL is not set
# CONFIG_FEATURE_CHAT_TTY_HIFI is not set
# CONFIG_FEATURE_CHAT_IMPLICIT_CR is not set
# CONFIG_FEATURE_CHAT_SWALLOW_OPTS is not set
# CONFIG_FEATURE_CHAT_SEND_ESCAPES is not set
# CONFIG_FEATURE_CHAT_VAR_ABORT_LEN is not set
# CONFIG_FEATURE_CHAT_CLR_ABORT is not set
# CONFIG_CONSPY is not set
CONFIG_CROND=y
# CONFIG_FEATURE_CROND_D is not set
# CONFIG_FEATURE_CROND_CALL_SENDMAIL is not set
CONFIG_FEATURE_CROND_SPECIAL_TIMES=y
CONFIG_FEATURE_CROND_DIR="/etc/cron"
CONFIG_CRONTAB=y
# CONFIG_DEVFSD is not set
# CONFIG_DEVFSD_MODLOAD is not set
# CONFIG_DEVFSD_FG_NP is not set
# CONFIG_DEVFSD_VERBOSE is not set
# CONFIG_FEATURE_DEVFS is not set
CONFIG_DEVMEM=y
# CONFIG_FBSPLASH is not set
CONFIG_FLASH_ERASEALL=y
# CONFIG_FLASH_LOCK is not set
CONFIG_FLASH_UNLOCK=y
CONFIG_FLASHCP=y
CONFIG_GETFATTR=y
# CONFIG_HDPARM is not set
# CONFIG_FEATURE_HDPARM_GET_IDENTITY is not set
# CONFIG_FEATURE_HDPARM_HDIO_SCAN_HWIF is not set
# CONFIG_FEATURE_HDPARM_HDIO_UNREGISTER_HWIF is not set
# CONFIG_FEATURE_HDPARM_HDIO_DRIVE_RESET is not set
# CONFIG_FEATURE_HDPARM_HDIO_TRISTATE_HWIF is not set
# CONFIG_FEATURE_HDPARM_HDIO_GETSET_DMA is not set
CONFIG_HEXEDIT=y
CONFIG_I2CGET=y
CONFIG_I2CSET=y
CONFIG_I2CDUMP=y
CONFIG_I2CDETECT=y
CONFIG_I2CTRANSFER=y
CONFIG_INOTIFYD=y
CONFIG_LESS=y
CONFIG_FEATURE_LESS_MAXLINES=9999999
CONFIG_FEATURE_LESS_BRACKETS=y
CONFIG_FEATURE_LESS_FLAGS=y
CONFIG_FEATURE_LESS_TRUNCATE=y
# CONFIG_FEATURE_LESS_MARKS is not set
CONFIG_FEATURE_LESS_REGEXP=y
CONFIG_FEATURE_LESS_WINCH=y
CONFIG_FEATURE_LESS_ASK_TERMINAL=y
# CONFIG_FEATURE_LESS_DASHCMD is not set
# CONFIG_FEATURE_LESS_LINENUMS is not set
# CONFIG_FEATURE_LESS_RAW is not set
# CONFIG_FEATURE_LESS_ENV is not set
# CONFIG_LSSCSI is not set
CONFIG_MAKEDEVS=y
# CONFIG_FEATURE_MAKEDEVS_LEAF is not set
CONFIG_FEATURE_MAKEDEVS_TABLE=y
# CONFIG_MAN is not set
CONFIG_MICROCOM=y
# CONFIG_MIM is not set
# CONFIG_MT is not set
# CONFIG_NANDWRITE is not set
# CONFIG_NANDDUMP is not set
CONFIG_PARTPROBE=y
# CONFIG_RAIDAUTORUN is not set
CONFIG_READAHEAD=y
# CONFIG_RFKILL is not set
# CONFIG_RUNLEVEL is not set
CONFIG_RX=y
CONFIG_SEEDRNG=y
# CONFIG_SETFATTR is not set
CONFIG_SETSERIAL=y
CONFIG_STRINGS=y
CONFIG_TIME=y
CONFIG_TREE=y
CONFIG_TS=y
# CONFIG_TTYSIZE is not set
# CONFIG_UBIATTACH is not set
# CONFIG_UBIDETACH is not set
# CONFIG_UBIMKVOL is not set
# CONFIG_UBIRMVOL is not set
# CONFIG_UBIRSVOL is not set
# CONFIG_UBIUPDATEVOL is not set
# CONFIG_UBIRENAME is not set
# CONFIG_VOLNAME is not set
CONFIG_WATCHDOG=y
# CONFIG_FEATURE_WATCHDOG_OPEN_TWICE is not set

#
# Networking Utilities
#
CONFIG_FEATURE_IPV6=y
# CONFIG_FEATURE_UNIX_LOCAL is not set
CONFIG_FEATURE_PREFER_IPV4_ADDRESS=y
# CONFIG_VERBOSE_RESOLUTION_ERRORS is not set
# CONFIG_FEATURE_ETC_NETWORKS is not set
# CONFIG_FEATURE_ETC_SERVICES is not set
# CONFIG_FEATURE_HWIB is not set
# CONFIG_FEATURE_TLS_SHA1 is not set
CONFIG_ARP=y
CONFIG_ARPING=y
CONFIG_BRCTL=y
CONFIG_FEATURE_BRCTL_FANCY=y
CONFIG_FEATURE_BRCTL_SHOW=y
CONFIG_DNSD=y
# CONFIG_ETHER_WAKE is not set
# CONFIG_FTPD is not set
# CONFIG_FEATURE_FTPD_WRITE is not set
# CONFIG_FEATURE_FTPD_ACCEPT_BROKEN_LIST is not set
# CONFIG_FEATURE_FTPD_AUTHENTICATION is not set
# CONFIG_FTPGET is not set
# CONFIG_FTPPUT is not set
# CONFIG_FEATURE_FTPGETPUT_LONG_OPTIONS is not set
CONFIG_HOSTNAME=y
# CONFIG_DNSDOMAINNAME is not set
CONFIG_HTTPD=y
CONFIG_FEATURE_HTTPD_PORT_DEFAULT=80
CONFIG_FEATURE_HTTPD_RANGES=y
# CONFIG_FEATURE_HTTPD_SETUID is not set
CONFIG_FEATURE_HTTPD_BASIC_AUTH=y
CONFIG_FEATURE_HTTPD_AUTH_MD5=y
CONFIG_FEATURE_HTTPD_CGI=y
CONFIG_FEATURE_HTTPD_CONFIG_WITH_SCRIPT_INTERPR=y
# CONFIG_FEATURE_HTTPD_SET_REMOTE_PORT_TO_ENV is not set
CONFIG_FEATURE_HTTPD_ENCODE_URL_STR=y
CONFIG_FEATURE_HTTPD_ERROR_PAGES=y
CONFIG_FEATURE_HTTPD_PROXY=y
CONFIG_FEATURE_HTTPD_GZIP=y
CONFIG_FEATURE_HTTPD_ETAG=y
CONFIG_FEATURE_HTTPD_LAST_MODIFIED=y
CONFIG_FEATURE_HTTPD_DATE=y
CONFIG_FEATURE_HTTPD_ACL_IP=y
CONFIG_IFCONFIG=y
CONFIG_FEATURE_IFCONFIG_STATUS=y
CONFIG_FEATURE_IFCONFIG_SLIP=y
CONFIG_FEATURE_IFCONFIG_MEMSTART_IOADDR_IRQ=y
CONFIG_FEATURE_IFCONFIG_HW=y
CONFIG_FEATURE_IFCONFIG_BROADCAST_PLUS=y
# CONFIG_IFENSLAVE is not set
# CONFIG_IFPLUGD is not set
CONFIG_IFUP=y
CONFIG_IFDOWN=y
CONFIG_IFUPDOWN_IFSTATE_PATH="/var/run/ifstate"
CONFIG_FEATURE_IFUPDOWN_IP=y
CONFIG_FEATURE_IFUPDOWN_IPV4=y
CONFIG_FEATURE_IFUPDOWN_IPV6=y
CONFIG_FEATURE_IFUPDOWN_MAPPING=y
# CONFIG_FEATURE_IFUPDOWN_EXTERNAL_DHCP is not set
# CONFIG_INETD is not set
# CONFIG_FEATURE_INETD_SUPPORT_BUILTIN_ECHO is not set
# CONFIG_FEATURE_INETD_SUPPORT_BUILTIN_DISCARD is not set
# CONFIG_FEATURE_INETD_SUPPORT_BUILTIN_TIME is not set
# CONFIG_FEATURE_INETD_SUPPORT_BUILTIN_DAYTIME is not set
# CONFIG_FEATURE_INETD_SUPPORT_BUILTIN_CHARGEN is not set
# CONFIG_FEATURE_INETD_RPC is not set
CONFIG_IP=y
# CONFIG_IPADDR is not set
# CONFIG_IPLINK is not set
# CONFIG_IPROUTE is not set
# CONFIG_IPTUNNEL is not set
# CONFIG_IPRULE is not set
# CONFIG_IPNEIGH is not set
CONFIG_FEATURE_IP_ADDRESS=y
CONFIG_FEATURE_IP_LINK=y
# CONFIG_FEATURE_IP_LINK_CAN is not set
CONFIG_FEATURE_IP_ROUTE=y
CONFIG_FEATURE_IP_ROUTE_DIR="/etc/iproute2"
CONFIG_FEATURE_IP_TUNNEL=y
CONFIG_FEATURE_IP_RULE=y
CONFIG_FEATURE_IP_NEIGH=y
# CONFIG_FEATURE_IP_RARE_PROTOCOLS is not set
# CONFIG_IPCALC is not set
# CONFIG_FEATURE_IPCALC_LONG_OPTIONS is not set
# CONFIG_FEATURE_IPCALC_FANCY is not set
# CONFIG_FAKEIDENTD is not set
# CONFIG_NAMEIF is not set
# CONFIG_FEATURE_NAMEIF_EXTENDED is not set
# CONFIG_NBDCLIENT is not set
CONFIG_NC=y
# CONFIG_NETCAT is not set
CONFIG_NC_SERVER=y
CONFIG_NC_EXTRA=y
# CONFIG_NC_110_COMPAT is not set
CONFIG_NETSTAT=y
CONFIG_FEATURE_NETSTAT_WIDE=y
CONFIG_FEATURE_NETSTAT_PRG=y
CONFIG_NSLOOKUP=y
CONFIG_FEATURE_NSLOOKUP_BIG=y
CONFIG_FEATURE_NSLOOKUP_LONG_OPTIONS=y
CONFIG_NTPD=y
CONFIG_FEATURE_NTPD_SERVER=y
CONFIG_FEATURE_NTPD_CONF=y
CONFIG_FEATURE_NTP_AUTH=y
CONFIG_PING=y
CONFIG_PING6=y
CONFIG_FEATURE_FANCY_PING=y
# CONFIG_PSCAN is not set
CONFIG_ROUTE=y
# CONFIG_SLATTACH is not set
# CONFIG_SSL_CLIENT is not set
# CONFIG_TC is not set
# CONFIG_FEATURE_TC_INGRESS is not set
# CONFIG_TCPSVD is not set
# CONFIG_UDPSVD is not set
CONFIG_TELNET=y
CONFIG_FEATURE_TELNET_TTYPE=y
CONFIG_FEATURE_TELNET_AUTOLOGIN=y
CONFIG_FEATURE_TELNET_WIDTH=y
# CONFIG_TELNETD is not set
# CONFIG_FEATURE_TELNETD_STANDALONE is not set
CONFIG_FEATURE_TELNETD_PORT_DEFAULT=0
# CONFIG_FEATURE_TELNETD_INETD_WAIT is not set
CONFIG_TFTP=y
CONFIG_FEATURE_TFTP_PROGRESS_BAR=y
CONFIG_FEATURE_TFTP_HPA_COMPAT=y
# CONFIG_TFTPD is not set
CONFIG_FEATURE_TFTP_GET=y
CONFIG_FEATURE_TFTP_PUT=y
CONFIG_FEATURE_TFTP_BLOCKSIZE=y
# CONFIG_TFTP_DEBUG is not set
# CONFIG_TLS is not set
CONFIG_TRACEROUTE=y
CONFIG_TRACEROUTE6=y
# CONFIG_FEATURE_TRACEROUTE_VERBOSE is not set
# CONFIG_FEATURE_TRACEROUTE_USE_ICMP is not set
CONFIG_TUNCTL=y
# CONFIG_FEATURE_TUNCTL_UG is not set
CONFIG_VCONFIG=y
CONFIG_WGET=y
CONFIG_FEATURE_WGET_LONG_OPTIONS=y
CONFIG_FEATURE_WGET_STATUSBAR=y
CONFIG_FEATURE_WGET_FTP=y
CONFIG_FEATURE_WGET_AUTHENTICATION=y
CONFIG_FEATURE_WGET_TIMEOUT=y
# CONFIG_FEATURE_WGET_HTTPS is not set
# CONFIG_FEATURE_WGET_OPENSSL is not set
# CONFIG_WHOIS is not set
# CONFIG_ZCIP is not set
CONFIG_UDHCPD=y
# CONFIG_FEATURE_UDHCPD_BOOTP is not set
# CONFIG_FEATURE_UDHCPD_BASE_IP_ON_MAC is not set
CONFIG_FEATURE_UDHCPD_WRITE_LEASES_EARLY=y
CONFIG_DHCPD_LEASES_FILE=""
CONFIG_DUMPLEASES=y
# CONFIG_DHCPRELAY is not set
CONFIG_UDHCPC=y
CONFIG_FEATURE_UDHCPC_ARPING=y
CONFIG_FEATURE_UDHCPC_SANITIZEOPT=y
CONFIG_UDHCPC_DEFAULT_SCRIPT="/usr/share/udhcpc/default.script"
CONFIG_UDHCPC6_DEFAULT_SCRIPT=""
# CONFIG_UDHCPC6 is not set
# CONFIG_FEATURE_UDHCPC6_RFC3646 is not set
# CONFIG_FEATURE_UDHCPC6_RFC4704 is not set
# CONFIG_FEATURE_UDHCPC6_RFC4833 is not set
# CONFIG_FEATURE_UDHCPC6_RFC5970 is not set

#
# Common options for DHCP applets
#
CONFIG_UDHCPC_DEFAULT_INTERFACE="eth0"
# CONFIG_FEATURE_UDHCP_PORT is not set
CONFIG_UDHCP_DEBUG=0
CONFIG_UDHCPC_SLACK_FOR_BUGGY_SERVERS=80
CONFIG_FEATURE_UDHCP_RFC3397=y
CONFIG_FEATURE_UDHCP_8021Q=y
CONFIG_IFUPDOWN_UDHCPC_CMD_OPTIONS="-x hostname:$(hostname) -S -T1 -t15 -R -b -O search"

#
# Print Utilities
#
# CONFIG_LPD is not set
# CONFIG_LPR is not set
# CONFIG_LPQ is not set

#
# Mail Utilities
#
CONFIG_FEATURE_MIME_CHARSET=""
# CONFIG_MAKEMIME is not set
# CONFIG_POPMAILDIR is not set
# CONFIG_FEATURE_POPMAILDIR_DELIVERY is not set
# CONFIG_REFORMIME is not set
# CONFIG_FEATURE_REFORMIME_COMPAT is not set
# CONFIG_SENDMAIL is not set

#
# Process Utilities
#
CONFIG_FEATURE_FAST_TOP=y
# CONFIG_FEATURE_SHOW_THREADS is not set
CONFIG_FREE=y
CONFIG_FUSER=y
# CONFIG_IOSTAT is not set
CONFIG_KILL=y
CONFIG_KILLALL=y
CONFIG_KILLALL5=y
CONFIG_LSOF=y
# CONFIG_MPSTAT is not set
# CONFIG_NMETER is not set
CONFIG_PGREP=y
CONFIG_PKILL=y
CONFIG_PIDOF=y
CONFIG_FEATURE_PIDOF_SINGLE=y
CONFIG_FEATURE_PIDOF_OMIT=y
CONFIG_PMAP=y
# CONFIG_POWERTOP is not set
# CONFIG_FEATURE_POWERTOP_INTERACTIVE is not set
CONFIG_PS=y
CONFIG_FEATURE_PS_WIDE=y
CONFIG_FEATURE_PS_LONG=y
# CONFIG_FEATURE_PS_TIME is not set
# CONFIG_FEATURE_PS_UNUSUAL_SYSTEMS is not set
# CONFIG_FEATURE_PS_ADDITIONAL_COLUMNS is not set
CONFIG_PSTREE=y
# CONFIG_PWDX is not set
CONFIG_SMEMCAP=y
CONFIG_BB_SYSCTL=y
CONFIG_TOP=y
CONFIG_FEATURE_TOP_INTERACTIVE=y
CONFIG_FEATURE_TOP_CPU_USAGE_PERCENTAGE=y
CONFIG_FEATURE_TOP_CPU_GLOBAL_PERCENTS=y
CONFIG_FEATURE_TOP_SMP_CPU=y
CONFIG_FEATURE_TOP_DECIMALS=y
CONFIG_FEATURE_TOP_SMP_PROCESS=y
CONFIG_FEATURE_TOPMEM=y
CONFIG_UPTIME=y
# CONFIG_FEATURE_UPTIME_UTMP_SUPPORT is not set
CONFIG_WATCH=y

#
# Runit Utilities
#
# CONFIG_CHPST is not set
# CONFIG_SETUIDGID is not set
# CONFIG_ENVUIDGID is not set
# CONFIG_ENVDIR is not set
# CONFIG_SOFTLIMIT is not set
# CONFIG_RUNSV is not set
# CONFIG_RUNSVDIR is not set
# CONFIG_FEATURE_RUNSVDIR_LOG is not set
# CONFIG_SV is not set
CONFIG_SV_DEFAULT_SERVICE_DIR=""
CONFIG_SVC=y
CONFIG_SVOK=y
# CONFIG_SVLOGD is not set
# CONFIG_CHCON is not set
# CONFIG_GETENFORCE is not set
# CONFIG_GETSEBOOL is not set
# CONFIG_LOAD_POLICY is not set
# CONFIG_MATCHPATHCON is not set
# CONFIG_RUNCON is not set
# CONFIG_SELINUXENABLED is not set
# CONFIG_SESTATUS is not set
# CONFIG_SETENFORCE is not set
# CONFIG_SETFILES is not set
# CONFIG_FEATURE_SETFILES_CHECK_OPTION is not set
# CONFIG_RESTORECON is not set
# CONFIG_SETSEBOOL is not set

#
# Shells
#
CONFIG_SH_IS_ASH=y
# CONFIG_SH_IS_HUSH is not set
# CONFIG_SH_IS_NONE is not set
# CONFIG_BASH_IS_ASH is not set
# CONFIG_BASH_IS_HUSH is not set
CONFIG_BASH_IS_NONE=y
CONFIG_SHELL_ASH=y
CONFIG_ASH=y
CONFIG_ASH_OPTIMIZE_FOR_SIZE=y
CONFIG_ASH_INTERNAL_GLOB=y
CONFIG_ASH_BASH_COMPAT=y
# CONFIG_ASH_BASH_SOURCE_CURDIR is not set
CONFIG_ASH_BASH_NOT_FOUND_HOOK=y
CONFIG_ASH_JOB_CONTROL=y
CONFIG_ASH_ALIAS=y
CONFIG_ASH_RANDOM_SUPPORT=y
CONFIG_ASH_EXPAND_PRMT=y
CONFIG_ASH_IDLE_TIMEOUT=y
# CONFIG_ASH_MAIL is not set
CONFIG_ASH_ECHO=y
CONFIG_ASH_PRINTF=y
CONFIG_ASH_TEST=y
CONFIG_ASH_HELP=y
CONFIG_ASH_GETOPTS=y
CONFIG_ASH_CMDCMD=y
# CONFIG_CTTYHACK is not set
# CONFIG_HUSH is not set
# CONFIG_SHELL_HUSH is not set
# CONFIG_HUSH_BASH_COMPAT is not set
# CONFIG_HUSH_BRACE_EXPANSION is not set
# CONFIG_HUSH_BASH_SOURCE_CURDIR is not set
# CONFIG_HUSH_LINENO_VAR is not set
# CONFIG_HUSH_INTERACTIVE is not set
# CONFIG_HUSH_SAVEHISTORY is not set
# CONFIG_HUSH_JOB is not set
# CONFIG_HUSH_TICK is not set
# CONFIG_HUSH_IF is not set
# CONFIG_HUSH_LOOPS is not set
# CONFIG_HUSH_CASE is not set
# CONFIG_HUSH_FUNCTIONS is not set
# CONFIG_HUSH_LOCAL is not set
# CONFIG_HUSH_RANDOM_SUPPORT is not set
# CONFIG_HUSH_MODE_X is not set
# CONFIG_HUSH_ECHO is not set
# CONFIG_HUSH_PRINTF is not set
# CONFIG_HUSH_TEST is not set
# CONFIG_HUSH_HELP is not set
# CONFIG_HUSH_EXPORT is not set
# CONFIG_HUSH_EXPORT_N is not set
# CONFIG_HUSH_READONLY is not set
# CONFIG_HUSH_KILL is not set
# CONFIG_HUSH_WAIT is not set
# CONFIG_HUSH_COMMAND is not set
# CONFIG_HUSH_TRAP is not set
# CONFIG_HUSH_TYPE is not set
# CONFIG_HUSH_TIMES is not set
# CONFIG_HUSH_READ is not set
# CONFIG_HUSH_SET is not set
# CONFIG_HUSH_UNSET is not set
# CONFIG_HUSH_ULIMIT is not set
# CONFIG_HUSH_UMASK is not set
# CONFIG_HUSH_GETOPTS is not set
# CONFIG_HUSH_MEMLEAK is not set

#
# Options common to all shells
#
CONFIG_FEATURE_SH_MATH=y
CONFIG_FEATURE_SH_MATH_64=y
CONFIG_FEATURE_SH_MATH_BASE=y
CONFIG_FEATURE_SH_EXTRA_QUIET=y
# CONFIG_FEATURE_SH_STANDALONE is not set
# CONFIG_FEATURE_SH_NOFORK is not set
CONFIG_FEATURE_SH_READ_FRAC=y
# CONFIG_FEATURE_SH_HISTFILESIZE is not set
CONFIG_FEATURE_SH_EMBEDDED_SCRIPTS=y

#
# System Logging Utilities
#
# CONFIG_KLOGD is not set
# CONFIG_FEATURE_KLOGD_KLOGCTL is not set
CONFIG_LOGGER=y
CONFIG_LOGREAD=y
# CONFIG_FEATURE_LOGREAD_REDUCED_LOCKING is not set
CONFIG_SYSLOGD=y
CONFIG_FEATURE_ROTATE_LOGFILE=y
CONFIG_FEATURE_REMOTE_LOG=y
CONFIG_FEATURE_SYSLOGD_DUP=y
# CONFIG_FEATURE_SYSLOGD_CFG is not set
# CONFIG_FEATURE_SYSLOGD_PRECISE_TIMESTAMPS is not set
CONFIG_FEATURE_SYSLOGD_READ_BUFFER_SIZE=256
CONFIG_FEATURE_IPC_SYSLOG=y
CONFIG_FEATURE_IPC_SYSLOG_BUFFER_SIZE=64
# CONFIG_FEATURE_KMSG_SYSLOG is not set
CONFIG_FEATURE_IP_LINK_CAN=n
